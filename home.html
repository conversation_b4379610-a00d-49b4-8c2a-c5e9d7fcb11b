<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>热力图轨迹演示</title>
  <script src="https://cdn.jsdelivr.net/npm/heatmap.js@2.0.5/build/heatmap.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/dat.gui/build/dat.gui.min.js"></script>
  <style>
    body { margin: 0; overflow: hidden; }
    #heatmapContainer {
      width: 100vw;
      height: 100vh;
      position: relative;
    }
    canvas {
      position: absolute;
      top: 0;
      left: 0;
    }
  </style>
</head>
<body>
  <div id="heatmapContainer"></div>

  <script>
    // 初始化 heatmap
    const heatmapInstance = h337.create({
      container: document.getElementById('heatmapContainer'),
      radius: 30,
      maxOpacity: 0.6,
      minOpacity: 0.1,
      blur: 0.85,
      gradient: {
        0.1: 'blue',
        0.3: 'purple',
        0.6: 'red',
        1.0: 'white'
      }
    });

    // 模拟轨迹点收集
    const container = document.getElementById('heatmapContainer');
    const points = [];
    let drawing = false;

    container.addEventListener('mousedown', () => drawing = true);
    container.addEventListener('mouseup', () => {
      drawing = false;
      heatmapInstance.setData({ max: 100, data: points });
    });

    container.addEventListener('mousemove', (e) => {
      if (!drawing) return;
      const rect = container.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      points.push({ x, y, value: 50 });
      heatmapInstance.addData({ x, y, value: 50 });
    });

    // 控制面板 dat.GUI
    const settings = {
      radius: 30,
      blur: 0.85,
      maxOpacity: 0.6,
      minOpacity: 0.1,
      clear: function () {
        points.length = 0;
        heatmapInstance.setData({ max: 100, data: [] });
      }
    };

    const gui = new dat.GUI();
    gui.add(settings, 'radius', 5, 100).onChange((v) => {
      heatmapInstance.configure({ radius: v });
    });
    gui.add(settings, 'blur', 0, 1).onChange((v) => {
      heatmapInstance.configure({ blur: v });
    });
    gui.add(settings, 'maxOpacity', 0, 1).onChange((v) => {
      heatmapInstance.configure({ maxOpacity: v });
    });
    gui.add(settings, 'minOpacity', 0, 1).onChange((v) => {
      heatmapInstance.configure({ minOpacity: v });
    });
    gui.add(settings, 'clear');
  </script>
</body>
</html>
