<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Liquid Metaball Simulation</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            min-height: 100vh;
            box-sizing: border-box;
        }
        
        .main-container {
            display: flex;
            width: 100%;
            gap: 20px;
        }
        
        .canvas-container {
            flex: 1;
            position: relative;
            background: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
            min-height: 600px;
        }
        
        canvas {
            display: block;
            cursor: crosshair;
            width: 100%;
            height: 100%;
            transition: cursor 0.2s ease;
        }
        
        canvas.dragging {
            cursor: grabbing;
        }
        
        canvas.hovering {
            cursor: grab;
        }
        
        canvas.drawing {
            cursor: crosshair;
        }
        
        .controls {
            width: 300px;
            background: #ffffff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            height: fit-content;
        }
        
        .controls h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 16px;
            font-weight: 600;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
            color: #555;
            font-weight: 500;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
            height: 6px;
            background: #ddd;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }
        
        .control-group input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            background: #007bff;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .control-group input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            background: #007bff;
            border-radius: 50%;
            cursor: pointer;
            border: none;
        }
        
        .palette-container {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        .palette-color {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .palette-color.active {
            border-color: #007bff;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: #007bff;
        }
        
        .checkbox-group label {
            margin: 0;
            font-size: 14px;
            color: #555;
        }
        
        .value-display {
            min-width: 30px;
            text-align: right;
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="canvas-container">
            <canvas id="canvas"></canvas>
        </div>
        
        <div class="controls">
            <h3>Metaball Controls</h3>
            
            <div class="control-group">
                <label>palette</label>
                <div class="palette-container">
                    <div class="palette-color active" style="background: linear-gradient(45deg, #88c0d0, #d08770);" data-palette="blue-orange"></div>
                    <div class="palette-color" style="background: linear-gradient(45deg, #b48ead, #d08770);" data-palette="purple-orange"></div>
                    <div class="palette-color" style="background: linear-gradient(45deg, #bf616a, #d08770);" data-palette="red-orange"></div>
                    <div class="palette-color" style="background: linear-gradient(45deg, #a3be8c, #ebcb8b);" data-palette="green-yellow"></div>
                    <div class="palette-color" style="background: #bf616a;" data-palette="red"></div>
                </div>
            </div>
            
            <div class="control-group">
                <label>age <span class="value-display" id="ageValue">10</span></label>
                <input type="range" id="age" min="1" max="20" value="10">
            </div>
            
            <div class="control-group">
                <label>power <span class="value-display" id="powerValue">10</span></label>
                <input type="range" id="power" min="1" max="20" value="10">
            </div>
            
            <div class="control-group">
                <label>distance <span class="value-display" id="distanceValue">10</span></label>
                <input type="range" id="distance" min="1" max="20" value="10">
            </div>
            
            <div class="control-group">
                <label>cutoff <span class="value-display" id="cutoffValue">0</span></label>
                <input type="range" id="cutoff" min="0" max="10" value="0">
            </div>
            
            <div class="control-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="smoothing" checked>
                    <label for="smoothing">smoothing</label>
                </div>
            </div>
            
            <div class="control-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="adjustPower" checked>
                    <label for="adjustPower">adjustPower</label>
                </div>
            </div>
            
            <div class="control-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="adjustIntensity" checked>
                    <label for="adjustIntensity">adjustIntensity</label>
                </div>
            </div>
            
            <div class="control-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="adjustRadius" checked>
                    <label for="adjustRadius">adjustRadius</label>
                </div>
            </div>
            
            <div class="control-group">
                <button id="clearTrail" style="width: 100%; padding: 8px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">Clear Trail</button>
            </div>
        </div>
    </div>

    <script>
        class MetaballSimulation {
            constructor() {
                this.canvas = document.getElementById('canvas');
                this.gl = this.canvas.getContext('webgl2') || this.canvas.getContext('webgl');
                
                if (!this.gl) {
                    alert('WebGL not supported');
                    return;
                }
                
                // 初始化参数
                this.metaballs = [];
                this.ballCount = 6;
                this.age = 10;
                this.power = 10;
                this.distance = 10;
                this.cutoff = 0;
                this.smoothing = true;
                this.adjustPower = true;
                this.adjustIntensity = true;
                this.adjustRadius = true;
                this.currentPalette = 'blue-orange';
                this.primaryColor = [0.533, 0.753, 0.816]; // #88c0d0
                this.secondaryColor = [0.816, 0.529, 0.439]; // #d08770
                
                // 鼠标交互和轨迹跟踪
                this.mouseX = 0;
                this.mouseY = 0;
                this.isDragging = false;
                this.draggedBall = -1;
                this.isDrawing = false;
                this.trailPoints = [];
                this.maxTrailPoints = 50;
                
                this.initWebGL();
                this.initMetaballs();
                this.setupEventListeners();
                this.resize();
                this.animate();
            }
            
            initWebGL() {
                // 顶点着色器
                const vertexShaderSource = `
                    attribute vec2 a_position;
                    void main() {
                        gl_Position = vec4(a_position, 0.0, 1.0);
                    }
                `;
                
                // 片段着色器 - 实现元球效果
                const fragmentShaderSource = `
                    precision highp float;
                    uniform vec2 u_resolution;
                    uniform float u_time;
                    uniform vec3 u_metaballs[12];
                    uniform int u_ballCount;
                    uniform float u_age;
                    uniform float u_power;
                    uniform float u_distance;
                    uniform float u_cutoff;
                    uniform bool u_smoothing;
                    uniform vec3 u_primaryColor;
                    uniform vec3 u_secondaryColor;
                    
                    // 噪声函数
                    float random(vec2 st) {
                        return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
                    }
                    
                    float noise(vec2 st) {
                        vec2 i = floor(st);
                        vec2 f = fract(st);
                        
                        float a = random(i);
                        float b = random(i + vec2(1.0, 0.0));
                        float c = random(i + vec2(0.0, 1.0));
                        float d = random(i + vec2(1.0, 1.0));
                        
                        vec2 u = f * f * (3.0 - 2.0 * f);
                        
                        return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
                    }
                    
                    void main() {
                        vec2 st = gl_FragCoord.xy / u_resolution.xy;
                        vec2 pos = st * 2.0 - 1.0;
                        pos.y *= u_resolution.y / u_resolution.x;
                        
                        float sum = 0.0;
                        vec3 color = vec3(0.0);
                        
                        // 计算所有元球的影响
                        for (int i = 0; i < 12; i++) {
                            if (i >= u_ballCount) break;
                            
                            vec2 ballPos = u_metaballs[i].xy;
                            float ballSize = u_metaballs[i].z;
                            
                            float dist = distance(pos, ballPos);
                            
                            // 使用power参数调节影响力计算
                            float influence = ballSize / (pow(dist * u_distance * 0.1, u_power * 0.2) + 0.01);
                            sum += influence;
                            
                            // 根据距离和age参数计算颜色混合
                            float ageFactor = influence * (u_age / 15.0);
                            vec3 mixedColor = mix(u_secondaryColor, u_primaryColor, ageFactor);
                            color += mixedColor * influence;
                        }
                        
                        // 应用cutoff阈值
                        float threshold = 0.5 + u_cutoff * 0.1;
                        float alpha;
                        
                        if (u_smoothing) {
                            alpha = smoothstep(threshold - 0.1, threshold + 0.1, sum);
                        } else {
                            alpha = step(threshold, sum);
                        }
                        
                        // 添加动态噪声效果
                        float dynamicNoise = noise(pos * 8.0 + u_time * 1.5) * 0.08;
                        alpha += dynamicNoise * (u_age / 20.0);
                        
                        // 边缘效果
                        float glow = smoothstep(threshold - 0.2, threshold, sum);
                        color += u_primaryColor * glow * 0.2;
                        
                        // 归一化颜色
                        if (sum > 0.0) {
                            color /= sum;
                        }
                        
                        // 调整最终颜色强度
                        color = clamp(color, 0.0, 1.0);
                        
                        gl_FragColor = vec4(color, clamp(alpha, 0.0, 1.0));
                    }
                `;
                
                // 编译着色器
                const vertexShader = this.createShader(this.gl.VERTEX_SHADER, vertexShaderSource);
                const fragmentShader = this.createShader(this.gl.FRAGMENT_SHADER, fragmentShaderSource);
                
                // 创建程序
                this.program = this.gl.createProgram();
                this.gl.attachShader(this.program, vertexShader);
                this.gl.attachShader(this.program, fragmentShader);
                this.gl.linkProgram(this.program);
                
                if (!this.gl.getProgramParameter(this.program, this.gl.LINK_STATUS)) {
                    console.error('Program link error:', this.gl.getProgramInfoLog(this.program));
                }
                
                // 获取uniform位置
                this.uniformLocations = {
                    resolution: this.gl.getUniformLocation(this.program, 'u_resolution'),
                    time: this.gl.getUniformLocation(this.program, 'u_time'),
                    metaballs: this.gl.getUniformLocation(this.program, 'u_metaballs'),
                    ballCount: this.gl.getUniformLocation(this.program, 'u_ballCount'),
                    age: this.gl.getUniformLocation(this.program, 'u_age'),
                    power: this.gl.getUniformLocation(this.program, 'u_power'),
                    distance: this.gl.getUniformLocation(this.program, 'u_distance'),
                    cutoff: this.gl.getUniformLocation(this.program, 'u_cutoff'),
                    smoothing: this.gl.getUniformLocation(this.program, 'u_smoothing'),
                    primaryColor: this.gl.getUniformLocation(this.program, 'u_primaryColor'),
                    secondaryColor: this.gl.getUniformLocation(this.program, 'u_secondaryColor')
                };
                
                // 创建全屏四边形
                const vertices = new Float32Array([
                    -1, -1,
                     1, -1,
                    -1,  1,
                     1,  1
                ]);
                
                this.vertexBuffer = this.gl.createBuffer();
                this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.vertexBuffer);
                this.gl.bufferData(this.gl.ARRAY_BUFFER, vertices, this.gl.STATIC_DRAW);
                
                const positionAttribute = this.gl.getAttribLocation(this.program, 'a_position');
                this.gl.enableVertexAttribArray(positionAttribute);
                this.gl.vertexAttribPointer(positionAttribute, 2, this.gl.FLOAT, false, 0, 0);
                
                // 启用混合
                this.gl.enable(this.gl.BLEND);
                this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE_MINUS_SRC_ALPHA);
            }
            
            createShader(type, source) {
                const shader = this.gl.createShader(type);
                this.gl.shaderSource(shader, source);
                this.gl.compileShader(shader);
                
                if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
                    console.error('Shader compile error:', this.gl.getShaderInfoLog(shader));
                    this.gl.deleteShader(shader);
                    return null;
                }
                
                return shader;
            }
            
            initMetaballs() {
                // 不再自动生成元球，改为根据鼠标轨迹动态生成
                this.metaballs = [];
                this.trailPoints = [];
            }
            
            setupEventListeners() {
                // 调色板事件
                document.querySelectorAll('.palette-color').forEach(palette => {
                    palette.addEventListener('click', (e) => {
                        document.querySelectorAll('.palette-color').forEach(p => p.classList.remove('active'));
                        e.target.classList.add('active');
                        this.currentPalette = e.target.dataset.palette;
                        this.updatePalette();
                    });
                });
                
                // 控制面板滑块事件
                document.getElementById('age').addEventListener('input', (e) => {
                    this.age = parseInt(e.target.value);
                    document.getElementById('ageValue').textContent = this.age;
                });
                
                document.getElementById('power').addEventListener('input', (e) => {
                    this.power = parseInt(e.target.value);
                    document.getElementById('powerValue').textContent = this.power;
                });
                
                document.getElementById('distance').addEventListener('input', (e) => {
                    this.distance = parseInt(e.target.value);
                    document.getElementById('distanceValue').textContent = this.distance;
                });
                
                document.getElementById('cutoff').addEventListener('input', (e) => {
                    this.cutoff = parseInt(e.target.value);
                    document.getElementById('cutoffValue').textContent = this.cutoff;
                });
                
                // 复选框事件
                document.getElementById('smoothing').addEventListener('change', (e) => {
                    this.smoothing = e.target.checked;
                });
                
                document.getElementById('adjustPower').addEventListener('change', (e) => {
                    this.adjustPower = e.target.checked;
                });
                
                document.getElementById('adjustIntensity').addEventListener('change', (e) => {
                    this.adjustIntensity = e.target.checked;
                });
                
                document.getElementById('adjustRadius').addEventListener('change', (e) => {
                    this.adjustRadius = e.target.checked;
                });
                
                // 清除轨迹按钮
                document.getElementById('clearTrail').addEventListener('click', () => {
                    this.clearTrail();
                });
                
                // 鼠标事件 - 类似热力图的绘制模式
                this.canvas.addEventListener('mousedown', (e) => {
                    const rect = this.canvas.getBoundingClientRect();
                    this.mouseX = ((e.clientX - rect.left) / rect.width) * 2 - 1;
                    this.mouseY = -(((e.clientY - rect.top) / rect.height) * 2 - 1);
                    // 修正宽高比
                    this.mouseY *= rect.height / rect.width;
                    
                    this.isDrawing = true;
                    this.isDragging = false;
                    
                    // 开始新的轨迹
                    this.trailPoints = [];
                    this.addTrailPoint(this.mouseX, this.mouseY);
                });
                
                this.canvas.addEventListener('mousemove', (e) => {
                    const rect = this.canvas.getBoundingClientRect();
                    this.mouseX = ((e.clientX - rect.left) / rect.width) * 2 - 1;
                    this.mouseY = -(((e.clientY - rect.top) / rect.height) * 2 - 1);
                    // 修正宽高比
                    this.mouseY *= rect.height / rect.width;
                    
                    // 类似热力图的绘制模式 - 只在按下鼠标时记录轨迹
                    if (this.isDrawing) {
                        this.addTrailPoint(this.mouseX, this.mouseY);
                        this.canvas.classList.add('drawing');
                    } else {
                        this.canvas.classList.remove('drawing');
                    }
                });
                
                this.canvas.addEventListener('mouseup', () => {
                    this.isDrawing = false;
                    this.canvas.classList.remove('drawing');
                    // 完成轨迹绘制后，将轨迹点转换为元球
                    this.generateMetaballsFromTrail();
                });
                
                // 添加鼠标离开画布事件
                this.canvas.addEventListener('mouseleave', () => {
                    this.isDrawing = false;
                    this.canvas.classList.remove('drawing');
                });
                
                // 双击清除轨迹
                this.canvas.addEventListener('dblclick', () => {
                    this.clearTrail();
                });
                
                // 窗口大小变化
                window.addEventListener('resize', () => this.resize());
            }
            
            updatePalette() {
                const palettes = {
                    'blue-orange': {
                        primary: [0.533, 0.753, 0.816], // #88c0d0
                        secondary: [0.816, 0.529, 0.439] // #d08770
                    },
                    'purple-orange': {
                        primary: [0.706, 0.557, 0.678], // #b48ead
                        secondary: [0.816, 0.529, 0.439] // #d08770
                    },
                    'red-orange': {
                        primary: [0.749, 0.380, 0.416], // #bf616a
                        secondary: [0.816, 0.529, 0.439] // #d08770
                    },
                    'green-yellow': {
                        primary: [0.639, 0.745, 0.549], // #a3be8c
                        secondary: [0.922, 0.796, 0.545] // #ebcb8b
                    },
                    'red': {
                        primary: [0.749, 0.380, 0.416], // #bf616a
                        secondary: [0.749, 0.380, 0.416] // #bf616a
                    }
                };
                
                if (palettes[this.currentPalette]) {
                    this.primaryColor = palettes[this.currentPalette].primary;
                    this.secondaryColor = palettes[this.currentPalette].secondary;
                }
            }
            
            addTrailPoint(x, y) {
                // 避免添加太接近的点
                if (this.trailPoints.length > 0) {
                    const lastPoint = this.trailPoints[this.trailPoints.length - 1];
                    const dist = Math.sqrt((x - lastPoint.x) ** 2 + (y - lastPoint.y) ** 2);
                    if (dist < 0.05) return; // 距离太近则不添加
                }
                
                this.trailPoints.push({
                    x: x,
                    y: y,
                    timestamp: Date.now(),
                    intensity: 1.0
                });
                
                // 限制轨迹点数量
                if (this.trailPoints.length > this.maxTrailPoints) {
                    this.trailPoints.shift();
                }
            }
            
            generateMetaballsFromTrail() {
                // 从轨迹点生成元球
                this.metaballs = [];
                
                // 对轨迹点进行采样，生成合适数量的元球
                const step = Math.max(1, Math.floor(this.trailPoints.length / this.ballCount));
                
                for (let i = 0; i < this.trailPoints.length; i += step) {
                    if (this.metaballs.length >= this.ballCount) break;
                    
                    const point = this.trailPoints[i];
                    const age = Date.now() - point.timestamp;
                    const fade = Math.max(0, 1 - age / 5000); // 5秒后完全消失
                    
                    if (fade > 0) {
                        this.metaballs.push({
                            x: point.x,
                            y: point.y,
                            vx: 0,
                            vy: 0,
                            size: (0.2 + Math.random() * 0.3) * fade,
                            originalSize: (0.2 + Math.random() * 0.3) * fade,
                            fade: fade,
                            birthTime: point.timestamp
                        });
                    }
                }
            }
            
            clearTrail() {
                this.trailPoints = [];
                this.metaballs = [];
            }
            
            resize() {
                const container = document.querySelector('.canvas-container');
                const rect = container.getBoundingClientRect();
                this.canvas.width = rect.width;
                this.canvas.height = rect.height;
                this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
            }
            
            update() {
                // 更新轨迹元球的淡出效果
                const currentTime = Date.now();
                
                // 过滤过期的轨迹点
                this.trailPoints = this.trailPoints.filter(point => {
                    const age = currentTime - point.timestamp;
                    return age < 10000; // 10秒后移除轨迹点
                });
                
                // 更新元球的淡出效果
                for (let i = this.metaballs.length - 1; i >= 0; i--) {
                    const ball = this.metaballs[i];
                    const age = currentTime - ball.birthTime;
                    ball.fade = Math.max(0, 1 - age / 8000); // 8秒后完全消失
                    
                    // 移除完全透明的元球
                    if (ball.fade <= 0) {
                        this.metaballs.splice(i, 1);
                        continue;
                    }
                    
                    // 根据参数和淡出调节大小
                    let sizeFactor = ball.fade;
                    if (this.adjustRadius) {
                        sizeFactor *= (this.distance / 10.0);
                    }
                    if (this.adjustPower) {
                        sizeFactor *= (this.power / 15.0);
                    }
                    ball.size = ball.originalSize * sizeFactor;
                }
                
                // 如果正在绘制，实时从轨迹生成元球
                if (this.isDrawing && this.trailPoints.length > 0) {
                    this.generateMetaballsFromTrail();
                }
            }
            
            render(time) {
                this.gl.clearColor(1.0, 1.0, 1.0, 1.0);
                this.gl.clear(this.gl.COLOR_BUFFER_BIT);
                
                this.gl.useProgram(this.program);
                
                // 设置uniform值
                this.gl.uniform2f(this.uniformLocations.resolution, this.canvas.width, this.canvas.height);
                this.gl.uniform1f(this.uniformLocations.time, time * 0.001);
                this.gl.uniform1i(this.uniformLocations.ballCount, Math.min(this.metaballs.length, 12));
                this.gl.uniform1f(this.uniformLocations.age, this.age);
                this.gl.uniform1f(this.uniformLocations.power, this.power);
                this.gl.uniform1f(this.uniformLocations.distance, this.distance);
                this.gl.uniform1f(this.uniformLocations.cutoff, this.cutoff);
                this.gl.uniform1i(this.uniformLocations.smoothing, this.smoothing);
                this.gl.uniform3fv(this.uniformLocations.primaryColor, this.primaryColor);
                this.gl.uniform3fv(this.uniformLocations.secondaryColor, this.secondaryColor);
                
                // 传递元球数据，包括淡出效果
                const metaballData = new Float32Array(36); // 12 balls * 3 components
                for (let i = 0; i < this.metaballs.length && i < 12; i++) {
                    const ball = this.metaballs[i];
                    metaballData[i * 3] = ball.x;
                    metaballData[i * 3 + 1] = ball.y;
                    metaballData[i * 3 + 2] = ball.size * (ball.fade || 1.0); // 应用淡出效果
                }
                this.gl.uniform3fv(this.uniformLocations.metaballs, metaballData);
                
                // 绘制
                this.gl.drawArrays(this.gl.TRIANGLE_STRIP, 0, 4);
            }
            
            animate() {
                const loop = (time) => {
                    this.update();
                    this.render(time);
                    requestAnimationFrame(loop);
                };
                requestAnimationFrame(loop);
            }
        }
        
        // 启动模拟
        window.addEventListener('load', () => {
            new MetaballSimulation();
        });
    </script>
</body>
</html>
