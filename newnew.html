<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebGL Interactive Heatmap</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        
        canvas {
            display: block;
            cursor: crosshair;
        }
        
        .controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
            min-width: 220px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .controls h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: block;
            color: #666;
            font-size: 12px;
            margin-bottom: 5px;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
            height: 6px;
            border-radius: 3px;
            background: #e0e0e0;
            outline: none;
            -webkit-appearance: none;
        }
        
        .control-group input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #007bff;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        }
        
        .control-group input[type="range"]::-moz-range-thumb {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #007bff;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        }
        
        .control-group input[type="number"] {
            width: 60px;
            padding: 2px 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        .control-group input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .color-palette {
            display: flex;
            gap: 5px;
            margin-top: 5px;
        }
        
        .color-option {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .color-option.active {
            border-color: #333;
        }
    </style>
</head>
<body>
    <canvas id="heatmapCanvas"></canvas>
    
    <div id="instructions" style="position: absolute; top: 20px; left: 20px; color: white; background: rgba(0,0,0,0.7); padding: 15px; border-radius: 8px; font-size: 14px; max-width: 300px; transition: opacity 0.3s ease;">
        <h4 style="margin: 0 0 10px 0;">🖌️ 按压式热力画笔</h4>
        <p style="margin: 5px 0;">• 按住鼠标左键拖动绘制</p>
        <p style="margin: 5px 0;">• 红色中心→蓝色→灰色外围</p>
        <p style="margin: 5px 0;">• 绘制后永久保留</p>
        <p style="margin: 5px 0;">• 支持触摸设备</p>
        <p style="margin: 5px 0; opacity: 0.7; font-size: 12px;">点击任意位置隐藏此提示</p>
    </div>
    
    <div class="controls">
        <h3>热力图控制面板</h3>
        
        <div class="control-group">
            <label>笔刷大小 (Brush Size)</label>
            <input type="range" id="powerSlider" min="10" max="50" value="25" step="1">
            <input type="number" id="powerValue" min="10" max="50" value="25" step="1">
        </div>
        
        <div class="control-group">
            <label>影响范围 (Range)</label>
            <input type="range" id="distanceSlider" min="0.3" max="1.5" value="0.8" step="0.1">
            <input type="number" id="distanceValue" min="0.3" max="1.5" value="0.8" step="0.1">
        </div>
        
        <div class="control-group">
            <label>最小显示阈值 (Threshold)</label>
            <input type="range" id="cutoffSlider" min="0" max="0.05" value="0.01" step="0.001">
            <input type="number" id="cutoffValue" min="0" max="0.05" value="0.01" step="0.001">
        </div>
        
        <div class="control-group">
            <button id="clearButton" style="width: 100%; padding: 8px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 10px;">清除画布</button>
        </div>
        
        <div class="control-group">
            <input type="checkbox" id="adjustPower" checked>
            <label for="adjustPower">增强效果 (Enhanced Effect)</label>
        </div>
        
        <div class="control-group">
            <input type="checkbox" id="adjustRadius" checked>
            <label for="adjustRadius">扩展范围 (Extended Range)</label>
        </div>
        
        <div class="control-group">
            <label>颜色方案 (Color Palette)</label>
            <div class="color-palette">
                <div class="color-option active" data-palette="0" style="background: linear-gradient(90deg, #000033, #0066ff, #66ccff);"></div>
                <div class="color-option" data-palette="1" style="background: linear-gradient(90deg, #330000, #ff3300, #ffcc66);"></div>
                <div class="color-option" data-palette="2" style="background: linear-gradient(90deg, #003300, #66ff00, #ccff66);"></div>
                <div class="color-option" data-palette="3" style="background: linear-gradient(90deg, #330033, #cc66ff, #ffccff);"></div>
            </div>
        </div>
    </div>

    <script>
        class WebGLHeatmap {
            constructor(canvas) {
                this.canvas = canvas;
                this.gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                
                if (!this.gl) {
                    console.error('WebGL not supported');
                    alert('你的浏览器不支持WebGL，请尝试使用最新版本的Chrome、Firefox或Safari浏览器');
                    throw new Error('WebGL not supported');
                }
                
                console.log('WebGL版本:', this.gl.getParameter(this.gl.VERSION));
                console.log('着色器版本:', this.gl.getParameter(this.gl.SHADING_LANGUAGE_VERSION));
                
                // 热力图参数
                this.params = {
                    age: 999999.0,   // 永不消失
                    power: 25.0,     // 更集中的强度
                    distance: 0.8,   // 更小的影响范围
                    cutoff: 0.01,    // 小的截断值
                    smoothing: true,
                    adjustPower: true,
                    adjustIntensity: true,
                    adjustRadius: true,
                    palette: 0
                };
                
                // 按压热力点
                this.heatPoints = [];
                this.maxPoints = 500; // 增加点数支持更长的绘制
                
                this.initWebGL();
                this.setupEventListeners();
                this.startAnimation();
            }
            
            initWebGL() {
                const gl = this.gl;
                
                // 顶点着色器
                const vertexShaderSource = `
                    attribute vec2 a_position;
                    attribute vec2 a_texCoord;
                    varying vec2 v_texCoord;
                    
                    void main() {
                        gl_Position = vec4(a_position, 0.0, 1.0);
                        v_texCoord = a_texCoord;
                    }
                `;
                
                // 片段着色器
                const fragmentShaderSource = `
                    precision mediump float;
                    
                    varying vec2 v_texCoord;
                    uniform vec2 u_resolution;
                    uniform float u_time;
                    uniform float u_age;
                    uniform float u_power;
                    uniform float u_distance;
                    uniform float u_cutoff;
                    uniform bool u_smoothing;
                    uniform bool u_adjustPower;
                    uniform bool u_adjustIntensity;
                    uniform bool u_adjustRadius;
                    uniform int u_palette;
                    uniform vec2 u_heatPoints[500];
                    uniform float u_heatTimes[500];
                    uniform float u_heatIntensities[500];
                    uniform int u_pointCount;
                    
                    vec3 getHeatColor(float intensity, int palette) {
                        intensity = clamp(intensity, 0.0, 1.0);
                        
                        if (palette == 0) {
                            // 按压效果：红色中心 -> 蓝色 -> 灰色外围
                            if (intensity < 0.05) {
                                return vec3(0.3, 0.3, 0.3); // 浅灰色外围
                            } else if (intensity < 0.2) {
                                // 灰色到蓝色过渡
                                return mix(vec3(0.3, 0.3, 0.3), vec3(0.0, 0.3, 0.8), (intensity - 0.05) / 0.15);
                            } else if (intensity < 0.5) {
                                // 蓝色到紫色过渡
                                return mix(vec3(0.0, 0.3, 0.8), vec3(0.4, 0.0, 0.8), (intensity - 0.2) / 0.3);
                            } else if (intensity < 0.8) {
                                // 紫色到红色过渡
                                return mix(vec3(0.4, 0.0, 0.8), vec3(0.9, 0.1, 0.1), (intensity - 0.5) / 0.3);
                            } else {
                                // 红色中心，更鲜艳的红色
                                return mix(vec3(0.9, 0.1, 0.1), vec3(1.0, 0.0, 0.0), (intensity - 0.8) / 0.2);
                            }
                        } else if (palette == 1) {
                            // 红色热力图
                            if (intensity < 0.1) {
                                return vec3(0.0, 0.0, 0.0);
                            } else if (intensity < 0.4) {
                                return mix(vec3(0.2, 0.0, 0.0), vec3(0.8, 0.0, 0.0), (intensity - 0.1) / 0.3);
                            } else if (intensity < 0.7) {
                                return mix(vec3(0.8, 0.0, 0.0), vec3(1.0, 0.4, 0.0), (intensity - 0.4) / 0.3);
                            } else {
                                return mix(vec3(1.0, 0.4, 0.0), vec3(1.0, 1.0, 0.4), (intensity - 0.7) / 0.3);
                            }
                        } else if (palette == 2) {
                            // 绿色热力图
                            if (intensity < 0.1) {
                                return vec3(0.0, 0.0, 0.0);
                            } else if (intensity < 0.4) {
                                return mix(vec3(0.0, 0.2, 0.0), vec3(0.0, 0.6, 0.0), (intensity - 0.1) / 0.3);
                            } else if (intensity < 0.7) {
                                return mix(vec3(0.0, 0.6, 0.0), vec3(0.4, 1.0, 0.0), (intensity - 0.4) / 0.3);
                            } else {
                                return mix(vec3(0.4, 1.0, 0.0), vec3(1.0, 1.0, 0.6), (intensity - 0.7) / 0.3);
                            }
                        } else {
                            // 紫色热力图
                            if (intensity < 0.1) {
                                return vec3(0.0, 0.0, 0.0);
                            } else if (intensity < 0.4) {
                                return mix(vec3(0.2, 0.0, 0.2), vec3(0.6, 0.0, 0.6), (intensity - 0.1) / 0.3);
                            } else if (intensity < 0.7) {
                                return mix(vec3(0.6, 0.0, 0.6), vec3(1.0, 0.2, 0.8), (intensity - 0.4) / 0.3);
                            } else {
                                return mix(vec3(1.0, 0.2, 0.8), vec3(1.0, 0.8, 1.0), (intensity - 0.7) / 0.3);
                            }
                        }
                    }
                    
                    void main() {
                        vec2 coord = gl_FragCoord.xy / u_resolution;
                        float totalIntensity = 0.0;
                        
                        for (int i = 0; i < 500; i++) {
                            if (i >= u_pointCount) break;
                            
                            vec2 point = u_heatPoints[i];
                            float pointIntensity = u_heatIntensities[i];
                            
                            float distance = length(coord - point) * u_distance;
                            
                            // 按压效果的强度计算 - 更集中的热力分布
                            float intensity = exp(-distance * distance * u_power) * pointIntensity;
                            
                            // 不应用时间衰减，永久保留
                            // 可选择性应用自适应调整
                            if (u_adjustPower) {
                                intensity = pow(intensity, 0.8);
                            }
                            
                            if (u_adjustRadius) {
                                intensity *= 1.2; // 固定增强因子
                            }
                            
                            totalIntensity += intensity;
                        }
                        
                        // 应用截断值
                        if (totalIntensity < u_cutoff) {
                            totalIntensity = 0.0;
                        }
                        
                        // 增强对比度
                        totalIntensity = pow(totalIntensity, 0.7);
                        
                        vec3 color = getHeatColor(totalIntensity, u_palette);
                        gl_FragColor = vec4(color, min(totalIntensity * 1.5, 1.0));
                    }
                `;
                
                // 编译着色器
                const vertexShader = this.createShader(gl.VERTEX_SHADER, vertexShaderSource);
                const fragmentShader = this.createShader(gl.FRAGMENT_SHADER, fragmentShaderSource);
                
                // 创建程序
                this.program = this.createProgram(vertexShader, fragmentShader);
                
                // 获取属性和uniform位置
                this.positionAttributeLocation = gl.getAttribLocation(this.program, 'a_position');
                this.texCoordAttributeLocation = gl.getAttribLocation(this.program, 'a_texCoord');
                
                this.resolutionUniformLocation = gl.getUniformLocation(this.program, 'u_resolution');
                this.timeUniformLocation = gl.getUniformLocation(this.program, 'u_time');
                this.ageUniformLocation = gl.getUniformLocation(this.program, 'u_age');
                this.powerUniformLocation = gl.getUniformLocation(this.program, 'u_power');
                this.distanceUniformLocation = gl.getUniformLocation(this.program, 'u_distance');
                this.cutoffUniformLocation = gl.getUniformLocation(this.program, 'u_cutoff');
                this.smoothingUniformLocation = gl.getUniformLocation(this.program, 'u_smoothing');
                this.adjustPowerUniformLocation = gl.getUniformLocation(this.program, 'u_adjustPower');
                this.adjustIntensityUniformLocation = gl.getUniformLocation(this.program, 'u_adjustIntensity');
                this.adjustRadiusUniformLocation = gl.getUniformLocation(this.program, 'u_adjustRadius');
                this.paletteUniformLocation = gl.getUniformLocation(this.program, 'u_palette');
                this.heatPointsUniformLocation = gl.getUniformLocation(this.program, 'u_heatPoints');
                this.heatTimesUniformLocation = gl.getUniformLocation(this.program, 'u_heatTimes');
                this.heatIntensitiesUniformLocation = gl.getUniformLocation(this.program, 'u_heatIntensities');
                this.pointCountUniformLocation = gl.getUniformLocation(this.program, 'u_pointCount');
                
                // 创建缓冲区
                this.createBuffers();
                
                // 设置混合
                gl.enable(gl.BLEND);
                gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
            }
            
            createShader(type, source) {
                const gl = this.gl;
                const shader = gl.createShader(type);
                gl.shaderSource(shader, source);
                gl.compileShader(shader);
                
                if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                    const error = gl.getShaderInfoLog(shader);
                    console.error('着色器编译错误:', error);
                    console.error('着色器源码:', source);
                    alert('着色器编译失败: ' + error);
                    gl.deleteShader(shader);
                    return null;
                }
                
                return shader;
            }
            
            createProgram(vertexShader, fragmentShader) {
                const gl = this.gl;
                const program = gl.createProgram();
                gl.attachShader(program, vertexShader);
                gl.attachShader(program, fragmentShader);
                gl.linkProgram(program);
                
                if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
                    console.error('Program linking error:', gl.getProgramInfoLog(program));
                    gl.deleteProgram(program);
                    return null;
                }
                
                return program;
            }
            
            createBuffers() {
                const gl = this.gl;
                
                // 创建全屏四边形
                const positions = [
                    -1, -1,  1, -1,  -1, 1,
                    -1, 1,   1, -1,   1, 1
                ];
                
                const texCoords = [
                    0, 0,  1, 0,  0, 1,
                    0, 1,  1, 0,  1, 1
                ];
                
                this.positionBuffer = gl.createBuffer();
                gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
                gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(positions), gl.STATIC_DRAW);
                
                this.texCoordBuffer = gl.createBuffer();
                gl.bindBuffer(gl.ARRAY_BUFFER, this.texCoordBuffer);
                gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(texCoords), gl.STATIC_DRAW);
            }
            
            setupEventListeners() {
                this.isDrawing = false; // 添加绘制状态标志
                
                // 鼠标按下事件
                this.canvas.addEventListener('mousedown', (e) => {
                    this.isDrawing = true;
                    const rect = this.canvas.getBoundingClientRect();
                    const x = (e.clientX - rect.left) / rect.width;
                    const y = 1.0 - (e.clientY - rect.top) / rect.height;
                    this.addHeatPoint(x, y);
                });
                
                // 鼠标移动事件 - 只在按下时绘制
                this.canvas.addEventListener('mousemove', (e) => {
                    if (!this.isDrawing) return;
                    
                    const rect = this.canvas.getBoundingClientRect();
                    const x = (e.clientX - rect.left) / rect.width;
                    const y = 1.0 - (e.clientY - rect.top) / rect.height;
                    this.addHeatPoint(x, y);
                });
                
                // 鼠标抬起事件
                this.canvas.addEventListener('mouseup', () => {
                    this.isDrawing = false;
                });
                
                // 鼠标离开画布事件
                this.canvas.addEventListener('mouseleave', () => {
                    this.isDrawing = false;
                });
                
                // 触摸事件支持（移动设备）
                this.canvas.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    this.isDrawing = true;
                    const rect = this.canvas.getBoundingClientRect();
                    const touch = e.touches[0];
                    const x = (touch.clientX - rect.left) / rect.width;
                    const y = 1.0 - (touch.clientY - rect.top) / rect.height;
                    this.addHeatPoint(x, y);
                });
                
                this.canvas.addEventListener('touchmove', (e) => {
                    e.preventDefault();
                    if (!this.isDrawing) return;
                    
                    const rect = this.canvas.getBoundingClientRect();
                    const touch = e.touches[0];
                    const x = (touch.clientX - rect.left) / rect.width;
                    const y = 1.0 - (touch.clientY - rect.top) / rect.height;
                    this.addHeatPoint(x, y);
                });
                
                this.canvas.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    this.isDrawing = false;
                });
                
                // 窗口大小变化
                window.addEventListener('resize', () => {
                    this.resizeCanvas();
                });
                
                this.resizeCanvas();
            }
            
            addHeatPoint(x, y) {
                const currentTime = performance.now() / 1000.0;
                
                // 按压效果的热力点生成 - 更密集的点分布
                if (this.heatPoints.length > 0) {
                    const lastPoint = this.heatPoints[this.heatPoints.length - 1];
                    const distance = Math.sqrt(
                        Math.pow(x - lastPoint.x, 2) + Math.pow(y - lastPoint.y, 2)
                    );
                    
                    // 降低距离阈值，使轨迹更连续
                    if (distance < 0.005 && (currentTime - lastPoint.time) < 0.02) {
                        return;
                    }
                }
                
                // 添加主要热力点
                this.heatPoints.push({
                    x: x,
                    y: y,
                    time: currentTime,
                    intensity: 1.0 // 主要按压点强度
                });
                
                // 添加周围的辅助点以增强按压效果
                const radius = 0.015; // 辅助点半径
                const numAuxPoints = 4; // 辅助点数量
                for (let i = 0; i < numAuxPoints; i++) {
                    const angle = (i / numAuxPoints) * Math.PI * 2;
                    const auxX = x + Math.cos(angle) * radius;
                    const auxY = y + Math.sin(angle) * radius;
                    
                    // 确保辅助点在画布范围内
                    if (auxX >= 0 && auxX <= 1 && auxY >= 0 && auxY <= 1) {
                        this.heatPoints.push({
                            x: auxX,
                            y: auxY,
                            time: currentTime,
                            intensity: 0.3 // 辅助点较低强度
                        });
                    }
                }
                
                // 限制点的数量
                if (this.heatPoints.length > this.maxPoints) {
                    // 移除最老的点
                    this.heatPoints.splice(0, this.heatPoints.length - this.maxPoints);
                }
            }
            
            resizeCanvas() {
                const dpr = window.devicePixelRatio || 1;
                this.canvas.width = window.innerWidth * dpr;
                this.canvas.height = window.innerHeight * dpr;
                this.canvas.style.width = window.innerWidth + 'px';
                this.canvas.style.height = window.innerHeight + 'px';
                
                if (this.gl) {
                    this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
                }
            }
            
            render() {
                const gl = this.gl;
                const currentTime = performance.now() / 1000.0;
                
                // 清除画布 - 纯黑色背景匹配图片效果
                gl.clearColor(0.0, 0.0, 0.0, 1.0);
                gl.clear(gl.COLOR_BUFFER_BIT);
                
                // 使用程序
                gl.useProgram(this.program);
                
                // 设置uniform值
                gl.uniform2f(this.resolutionUniformLocation, this.canvas.width, this.canvas.height);
                gl.uniform1f(this.timeUniformLocation, currentTime);
                gl.uniform1f(this.ageUniformLocation, this.params.age);
                gl.uniform1f(this.powerUniformLocation, this.params.power);
                gl.uniform1f(this.distanceUniformLocation, this.params.distance);
                gl.uniform1f(this.cutoffUniformLocation, this.params.cutoff);
                gl.uniform1i(this.smoothingUniformLocation, this.params.smoothing ? 1 : 0);
                gl.uniform1i(this.adjustPowerUniformLocation, this.params.adjustPower ? 1 : 0);
                gl.uniform1i(this.adjustIntensityUniformLocation, this.params.adjustIntensity ? 1 : 0);
                gl.uniform1i(this.adjustRadiusUniformLocation, this.params.adjustRadius ? 1 : 0);
                gl.uniform1i(this.paletteUniformLocation, this.params.palette);
                
                // 检查WebGL错误
                const error = gl.getError();
                if (error !== gl.NO_ERROR) {
                    console.error('WebGL Error during uniform setup:', error);
                }
                
                // 不过滤点 - 永久保留所有热力点
                const points = this.heatPoints;
                
                // 设置热力点数据
                const pointPositions = new Float32Array(this.maxPoints * 2);
                const pointTimes = new Float32Array(this.maxPoints);
                const pointIntensities = new Float32Array(this.maxPoints);
                
                for (let i = 0; i < Math.min(points.length, this.maxPoints); i++) {
                    pointPositions[i * 2] = points[i].x;
                    pointPositions[i * 2 + 1] = points[i].y;
                    pointTimes[i] = points[i].time;
                    pointIntensities[i] = points[i].intensity || 1.0;
                }
                
                gl.uniform2fv(this.heatPointsUniformLocation, pointPositions);
                gl.uniform1fv(this.heatTimesUniformLocation, pointTimes);
                gl.uniform1fv(this.heatIntensitiesUniformLocation, pointIntensities);
                gl.uniform1i(this.pointCountUniformLocation, Math.min(points.length, this.maxPoints));
                
                // 设置顶点属性
                gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
                gl.enableVertexAttribArray(this.positionAttributeLocation);
                gl.vertexAttribPointer(this.positionAttributeLocation, 2, gl.FLOAT, false, 0, 0);
                
                gl.bindBuffer(gl.ARRAY_BUFFER, this.texCoordBuffer);
                gl.enableVertexAttribArray(this.texCoordAttributeLocation);
                gl.vertexAttribPointer(this.texCoordAttributeLocation, 2, gl.FLOAT, false, 0, 0);
                
                // 绘制
                gl.drawArrays(gl.TRIANGLES, 0, 6);
            }
            
            startAnimation() {
                const animate = () => {
                    this.render();
                    requestAnimationFrame(animate);
                };
                animate();
            }
            

            
            updateParams(newParams) {
                Object.assign(this.params, newParams);
            }
            
            clearCanvas() {
                this.heatPoints = [];
            }
        }
        
        // 初始化应用
        window.addEventListener('DOMContentLoaded', () => {
            const canvas = document.getElementById('heatmapCanvas');
            const heatmap = new WebGLHeatmap(canvas);
            
            // 点击隐藏指令
            const instructions = document.getElementById('instructions');
            document.addEventListener('click', () => {
                if (instructions) {
                    instructions.style.opacity = '0';
                    instructions.style.pointerEvents = 'none';
                    setTimeout(() => {
                        if (instructions.parentNode) {
                            instructions.parentNode.removeChild(instructions);
                        }
                    }, 300);
                }
            }, { once: true });
            
            // 控制面板事件监听
            function setupControls() {
                const controls = {
                    power: { slider: 'powerSlider', value: 'powerValue' },
                    distance: { slider: 'distanceSlider', value: 'distanceValue' },
                    cutoff: { slider: 'cutoffSlider', value: 'cutoffValue' }
                };
                
                // 数值控制
                Object.keys(controls).forEach(param => {
                    const slider = document.getElementById(controls[param].slider);
                    const valueInput = document.getElementById(controls[param].value);
                    
                    slider.addEventListener('input', (e) => {
                        const value = parseFloat(e.target.value);
                        valueInput.value = value;
                        heatmap.updateParams({ [param]: value });
                    });
                    
                    valueInput.addEventListener('input', (e) => {
                        const value = parseFloat(e.target.value);
                        slider.value = value;
                        heatmap.updateParams({ [param]: value });
                    });
                });
                
                // 布尔控制
                ['adjustPower', 'adjustRadius'].forEach(param => {
                    const checkbox = document.getElementById(param);
                    if (checkbox) {
                        checkbox.addEventListener('change', (e) => {
                            heatmap.updateParams({ [param]: e.target.checked });
                        });
                    }
                });
                
                // 清除按钮
                const clearButton = document.getElementById('clearButton');
                if (clearButton) {
                    clearButton.addEventListener('click', () => {
                        heatmap.clearCanvas();
                    });
                }
                
                // 颜色方案选择
                document.querySelectorAll('.color-option').forEach((option, index) => {
                    option.addEventListener('click', () => {
                        document.querySelector('.color-option.active').classList.remove('active');
                        option.classList.add('active');
                        heatmap.updateParams({ palette: index });
                    });
                });
            }
            
            setupControls();
        });
    </script>
</body>
</html>
