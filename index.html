<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Heatmap Drawing</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            width: 100%;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: #333;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.8;
            font-size: 14px;
        }

        .canvas-container {
            position: relative;
            display: flex;
            justify-content: center;
            padding: 20px;
            background: #fafafa;
        }

        #heatmapCanvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            cursor: crosshair;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        #heatmapCanvas:active {
            cursor: grabbing;
        }

        .controls {
            padding: 20px;
            background: #f8f8f8;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            font-weight: bold;
            color: #666;
        }

        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        button:hover {
            background: #0056b3;
        }

        button:active {
            transform: translateY(1px);
        }

        .status {
            padding: 10px 20px;
            background: #e9ecef;
            text-align: center;
            font-size: 12px;
            color: #666;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }

            .canvas-container {
                padding: 10px;
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Interactive Heatmap Drawing</h1>
            <p>Click and drag to create heat effects. Red represents maximum intensity, transitioning through blue to gray at the edges.</p>
        </div>

        <div class="canvas-container">
            <canvas id="heatmapCanvas" width="800" height="600"></canvas>
        </div>

        <div class="controls">
            <div class="control-group">
                <button id="clearCanvas">Clear Canvas</button>
            </div>
        </div>

        <div class="status">
            <span id="statusText">Ready to draw - Click and drag to create heat effects</span>
        </div>
    </div>

    <script>
        // Heatmap Drawing Application
        class HeatmapDrawer {
            constructor(canvasId) {
                this.canvas = document.getElementById(canvasId);
                this.ctx = this.canvas.getContext('2d');
                this.isDrawing = false;
                this.statusText = document.getElementById('statusText');

                this.setupCanvas();
                this.setupEventListeners();
                this.updateStatus('Ready to draw');
            }

            setupCanvas() {
                // Set up canvas for high DPI displays
                const rect = this.canvas.getBoundingClientRect();
                const dpr = window.devicePixelRatio || 1;

                this.canvas.width = rect.width * dpr;
                this.canvas.height = rect.height * dpr;
                this.ctx.scale(dpr, dpr);

                // Set canvas display size
                this.canvas.style.width = rect.width + 'px';
                this.canvas.style.height = rect.height + 'px';

                // Set initial canvas background
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            }

            setupEventListeners() {
                // Mouse events
                this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
                this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
                this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
                this.canvas.addEventListener('mouseleave', (e) => this.handleMouseUp(e));

                // Touch events for mobile support
                this.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e));
                this.canvas.addEventListener('touchmove', (e) => this.handleTouchMove(e));
                this.canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e));

                // Clear button
                document.getElementById('clearCanvas').addEventListener('click', () => this.clearCanvas());

                // Prevent context menu on right click
                this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
            }

            getMousePos(e) {
                const rect = this.canvas.getBoundingClientRect();
                return {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };
            }

            getTouchPos(e) {
                const rect = this.canvas.getBoundingClientRect();
                return {
                    x: e.touches[0].clientX - rect.left,
                    y: e.touches[0].clientY - rect.top
                };
            }

            handleMouseDown(e) {
                this.isDrawing = true;
                const pos = this.getMousePos(e);
                this.drawHeatPoint(pos.x, pos.y);
                this.updateStatus('Drawing heat effect...');
            }

            handleMouseMove(e) {
                if (!this.isDrawing) return;

                const pos = this.getMousePos(e);
                this.drawHeatPoint(pos.x, pos.y);
            }

            handleMouseUp(e) {
                if (this.isDrawing) {
                    this.isDrawing = false;
                    this.updateStatus('Ready to draw');
                }
            }

            handleTouchStart(e) {
                e.preventDefault();
                this.isDrawing = true;
                const pos = this.getTouchPos(e);
                this.drawHeatPoint(pos.x, pos.y);
                this.updateStatus('Drawing heat effect...');
            }

            handleTouchMove(e) {
                e.preventDefault();
                if (!this.isDrawing) return;

                const pos = this.getTouchPos(e);
                this.drawHeatPoint(pos.x, pos.y);
            }

            handleTouchEnd(e) {
                e.preventDefault();
                if (this.isDrawing) {
                    this.isDrawing = false;
                    this.updateStatus('Ready to draw');
                }
            }

            drawHeatPoint(x, y) {
                const radius = 40; // Heat effect radius

                // Create radial gradient: Red (center) -> Blue -> Gray (edge)
                const gradient = this.ctx.createRadialGradient(x, y, 0, x, y, radius);

                // Color stops for heat effect
                gradient.addColorStop(0, 'rgba(255, 0, 0, 0.8)');     // Red center (hottest)
                gradient.addColorStop(0.3, 'rgba(255, 100, 0, 0.6)'); // Orange-red
                gradient.addColorStop(0.6, 'rgba(0, 100, 255, 0.4)'); // Blue
                gradient.addColorStop(0.8, 'rgba(100, 100, 255, 0.2)'); // Light blue
                gradient.addColorStop(1, 'rgba(128, 128, 128, 0.1)');   // Gray edge (coolest)

                // Set blend mode for natural overlapping
                this.ctx.globalCompositeOperation = 'screen';

                // Draw the heat point
                this.ctx.fillStyle = gradient;
                this.ctx.beginPath();
                this.ctx.arc(x, y, radius, 0, Math.PI * 2);
                this.ctx.fill();

                // Reset blend mode
                this.ctx.globalCompositeOperation = 'source-over';
            }

            clearCanvas() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                this.updateStatus('Canvas cleared - Ready to draw');
            }

            updateStatus(message) {
                this.statusText.textContent = message;
            }
        }

        // Initialize the application when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            const heatmapDrawer = new HeatmapDrawer('heatmapCanvas');
        });
    </script>
</body>
</html>