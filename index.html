<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Heatmap Drawing</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            width: 100%;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: #333;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.8;
            font-size: 14px;
        }

        .canvas-container {
            position: relative;
            display: flex;
            justify-content: center;
            padding: 20px;
            background: #fafafa;
        }

        #heatmapCanvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            cursor: crosshair;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        #heatmapCanvas:active {
            cursor: grabbing;
        }

        .controls {
            padding: 20px;
            background: #f8f8f8;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            font-weight: bold;
            color: #666;
        }

        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        button:hover {
            background: #0056b3;
        }

        button:active {
            transform: translateY(1px);
        }

        .status {
            padding: 10px 20px;
            background: #e9ecef;
            text-align: center;
            font-size: 12px;
            color: #666;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }

            .canvas-container {
                padding: 10px;
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Natural Heat Trace Drawing</h1>
            <p>模拟手指在感温泥土上留下痕迹 - 轻触产生淡痕，长按或停留会产生更深更大的热力印记</p>
        </div>

        <div class="canvas-container">
            <canvas id="heatmapCanvas" width="800" height="600"></canvas>
        </div>

        <div class="controls">
            <div class="control-group">
                <button id="clearCanvas">Clear Canvas</button>
            </div>
        </div>

        <div class="status">
            <span id="statusText">准备绘制 - 点击并按住创建自然热力痕迹，停留时间越长痕迹越深</span>
        </div>
    </div>

    <script>
        // Natural Heatmap Drawing Application
        class HeatmapDrawer {
            constructor(canvasId) {
                this.canvas = document.getElementById(canvasId);
                this.ctx = this.canvas.getContext('2d');
                this.isDrawing = false;
                this.statusText = document.getElementById('statusText');

                // Heat accumulation system
                this.currentHeatPoint = null;
                this.heatStartTime = 0;
                this.animationFrame = null;
                this.heatAccumulationRate = 0.02; // Heat intensity increase per frame
                this.maxHeatIntensity = 1.0;
                this.baseRadius = 20;
                this.maxRadius = 80;

                // Track heat points for continuous drawing
                this.activeHeatPoints = new Map();
                this.lastMousePos = null;

                this.setupCanvas();
                this.setupEventListeners();
                this.updateStatus('Ready to draw - Press and hold for deeper heat traces');
            }

            setupCanvas() {
                // Set canvas size directly from HTML attributes
                const canvasWidth = this.canvas.width;
                const canvasHeight = this.canvas.height;

                // Ensure canvas has proper dimensions
                this.canvas.style.width = canvasWidth + 'px';
                this.canvas.style.height = canvasHeight + 'px';

                // Set initial canvas background
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, canvasWidth, canvasHeight);

                // Add debug info
                console.log('Canvas setup:', {
                    width: this.canvas.width,
                    height: this.canvas.height,
                    styleWidth: this.canvas.style.width,
                    styleHeight: this.canvas.style.height
                });
            }

            setupEventListeners() {
                // Mouse events
                this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
                this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
                this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
                this.canvas.addEventListener('mouseleave', (e) => this.handleMouseUp(e));

                // Touch events for mobile support
                this.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e));
                this.canvas.addEventListener('touchmove', (e) => this.handleTouchMove(e));
                this.canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e));

                // Clear button
                document.getElementById('clearCanvas').addEventListener('click', () => this.clearCanvas());

                // Prevent context menu on right click
                this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
            }

            getMousePos(e) {
                const rect = this.canvas.getBoundingClientRect();
                return {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };
            }

            getTouchPos(e) {
                const rect = this.canvas.getBoundingClientRect();
                return {
                    x: e.touches[0].clientX - rect.left,
                    y: e.touches[0].clientY - rect.top
                };
            }

            handleMouseDown(e) {
                this.isDrawing = true;
                const pos = this.getMousePos(e);
                this.lastMousePos = pos;

                // Start heat accumulation at this point
                this.startHeatAccumulation(pos.x, pos.y);
                this.updateStatus('Building heat intensity...');
            }

            handleMouseMove(e) {
                if (!this.isDrawing) return;

                const pos = this.getMousePos(e);
                const distance = this.getDistance(this.lastMousePos, pos);

                // If mouse moved significantly, start new heat point
                if (distance > 5) {
                    this.stopHeatAccumulation();
                    this.startHeatAccumulation(pos.x, pos.y);
                    this.lastMousePos = pos;
                }
            }

            handleMouseUp(e) {
                if (this.isDrawing) {
                    this.isDrawing = false;
                    this.stopHeatAccumulation();
                    this.updateStatus('Ready to draw - Press and hold for deeper heat traces');
                }
            }

            handleTouchStart(e) {
                e.preventDefault();
                this.isDrawing = true;
                const pos = this.getTouchPos(e);
                this.lastMousePos = pos;
                this.startHeatAccumulation(pos.x, pos.y);
                this.updateStatus('Building heat intensity...');
            }

            handleTouchMove(e) {
                e.preventDefault();
                if (!this.isDrawing) return;

                const pos = this.getTouchPos(e);
                const distance = this.getDistance(this.lastMousePos, pos);

                if (distance > 5) {
                    this.stopHeatAccumulation();
                    this.startHeatAccumulation(pos.x, pos.y);
                    this.lastMousePos = pos;
                }
            }

            handleTouchEnd(e) {
                e.preventDefault();
                if (this.isDrawing) {
                    this.isDrawing = false;
                    this.stopHeatAccumulation();
                    this.updateStatus('Ready to draw - Press and hold for deeper heat traces');
                }
            }

            // Utility function to calculate distance between two points
            getDistance(pos1, pos2) {
                if (!pos1 || !pos2) return 0;
                const dx = pos1.x - pos2.x;
                const dy = pos1.y - pos2.y;
                return Math.sqrt(dx * dx + dy * dy);
            }

            // Start heat accumulation at a specific point
            startHeatAccumulation(x, y) {
                console.log('Starting heat accumulation at:', x, y);

                // Stop any existing animation
                this.stopHeatAccumulation();

                this.currentHeatPoint = {
                    x: x,
                    y: y,
                    intensity: 0.1, // Start with minimal intensity
                    startTime: Date.now()
                };

                // Draw initial heat point immediately
                this.drawNaturalHeatPoint(x, y, 0.1);

                // Start the animation loop for heat accumulation
                this.animateHeatAccumulation();
            }

            // Stop heat accumulation
            stopHeatAccumulation() {
                if (this.animationFrame) {
                    cancelAnimationFrame(this.animationFrame);
                    this.animationFrame = null;
                }
                this.currentHeatPoint = null;
            }

            // Animation loop for heat accumulation
            animateHeatAccumulation() {
                if (!this.currentHeatPoint || !this.isDrawing) {
                    console.log('Animation stopped - currentHeatPoint:', !!this.currentHeatPoint, 'isDrawing:', this.isDrawing);
                    return;
                }

                // Calculate time-based intensity
                const currentTime = Date.now();
                const elapsedTime = (currentTime - this.currentHeatPoint.startTime) / 1000; // seconds

                // Increase intensity over time with diminishing returns
                this.currentHeatPoint.intensity = Math.min(
                    this.maxHeatIntensity,
                    0.1 + (elapsedTime * 0.3) // Gradual intensity increase
                );

                console.log('Animating heat - elapsed:', elapsedTime, 'intensity:', this.currentHeatPoint.intensity);

                // Draw the current heat point
                this.drawNaturalHeatPoint(
                    this.currentHeatPoint.x,
                    this.currentHeatPoint.y,
                    this.currentHeatPoint.intensity
                );

                // Continue animation
                this.animationFrame = requestAnimationFrame(() => this.animateHeatAccumulation());
            }

            // Draw natural heat point with time-based intensity
            drawNaturalHeatPoint(x, y, intensity) {
                console.log('Drawing natural heat point at:', x, y, 'intensity:', intensity);

                // First draw a simple test circle to ensure basic drawing works
                this.ctx.fillStyle = 'red';
                this.ctx.beginPath();
                this.ctx.arc(x, y, 5, 0, Math.PI * 2);
                this.ctx.fill();

                // Calculate radius based on intensity (heat spreads over time)
                const currentRadius = this.baseRadius + (this.maxRadius - this.baseRadius) * intensity;

                // Create multiple layers for natural heat diffusion
                this.drawHeatLayer(x, y, currentRadius * 0.3, intensity * 1.0, 'core');
                this.drawHeatLayer(x, y, currentRadius * 0.6, intensity * 0.7, 'middle');
                this.drawHeatLayer(x, y, currentRadius * 1.0, intensity * 0.4, 'outer');
            }

            // Draw individual heat layer
            drawHeatLayer(x, y, radius, intensity, layer) {
                console.log('Drawing heat layer:', layer, 'at', x, y, 'radius:', radius, 'intensity:', intensity);

                // Ensure radius is valid
                if (radius <= 0) return;

                const gradient = this.ctx.createRadialGradient(x, y, 0, x, y, radius);

                // Simplified color profiles for better visibility
                if (layer === 'core') {
                    // Hot core - deep red
                    gradient.addColorStop(0, `rgba(255, 0, 0, ${Math.min(1, intensity * 0.9)})`);
                    gradient.addColorStop(0.5, `rgba(255, 50, 0, ${Math.min(1, intensity * 0.6)})`);
                    gradient.addColorStop(1, `rgba(255, 100, 0, ${Math.min(1, intensity * 0.2)})`);
                } else if (layer === 'middle') {
                    // Warm middle - orange to blue transition
                    gradient.addColorStop(0, `rgba(255, 100, 0, ${Math.min(1, intensity * 0.5)})`);
                    gradient.addColorStop(0.6, `rgba(255, 150, 50, ${Math.min(1, intensity * 0.3)})`);
                    gradient.addColorStop(1, `rgba(100, 150, 255, ${Math.min(1, intensity * 0.1)})`);
                } else {
                    // Cool outer - blue to gray
                    gradient.addColorStop(0, `rgba(100, 150, 255, ${Math.min(1, intensity * 0.3)})`);
                    gradient.addColorStop(0.7, `rgba(150, 150, 200, ${Math.min(1, intensity * 0.2)})`);
                    gradient.addColorStop(1, `rgba(180, 180, 180, ${Math.min(1, intensity * 0.05)})`);
                }

                // Use normal blending first to ensure visibility
                this.ctx.globalCompositeOperation = 'source-over';
                this.ctx.fillStyle = gradient;
                this.ctx.beginPath();
                this.ctx.arc(x, y, radius, 0, Math.PI * 2);
                this.ctx.fill();
            }

            clearCanvas() {
                // Stop any ongoing heat accumulation
                this.stopHeatAccumulation();

                // Clear the canvas
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // Reset state
                this.activeHeatPoints.clear();
                this.lastMousePos = null;

                this.updateStatus('Canvas cleared - Ready to create natural heat traces');
            }

            updateStatus(message) {
                this.statusText.textContent = message;
            }
        }

        // Initialize the application when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            const heatmapDrawer = new HeatmapDrawer('heatmapCanvas');
        });
    </script>
</body>
</html>