<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Heatmap Drawing</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            width: 100%;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: #333;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.8;
            font-size: 14px;
        }

        .canvas-container {
            position: relative;
            display: flex;
            justify-content: center;
            padding: 20px;
            background: #fafafa;
        }

        #heatmapCanvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            cursor: crosshair;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        #heatmapCanvas:active {
            cursor: grabbing;
        }

        .controls {
            padding: 20px;
            background: #f8f8f8;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            font-weight: bold;
            color: #666;
        }

        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        button:hover {
            background: #0056b3;
        }

        button:active {
            transform: translateY(1px);
        }

        .status {
            padding: 10px 20px;
            background: #e9ecef;
            text-align: center;
            font-size: 12px;
            color: #666;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }

            .canvas-container {
                padding: 10px;
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Natural Heat Trace Drawing</h1>
            <p>模拟手指在感温泥土上留下痕迹 - 轻触产生淡痕，长按或停留会产生更深更大的热力印记</p>
        </div>

        <div class="canvas-container">
            <canvas id="heatmapCanvas" width="800" height="600"></canvas>
        </div>

        <div class="controls">
            <div class="control-group">
                <button id="clearCanvas">Clear Canvas</button>
            </div>
        </div>

        <div class="status">
            <span id="statusText">准备绘制 - 点击并按住创建自然热力痕迹，停留时间越长痕迹越深</span>
        </div>
    </div>

    <script>
        // Natural Heatmap Drawing Application
        class HeatmapDrawer {
            constructor(canvasId) {
                this.canvas = document.getElementById(canvasId);
                this.ctx = this.canvas.getContext('2d');
                this.isDrawing = false;
                this.statusText = document.getElementById('statusText');

                // Heat accumulation system
                this.currentHeatPoint = null;
                this.heatStartTime = 0;
                this.animationFrame = null;
                this.heatAccumulationRate = 0.02; // Heat intensity increase per frame
                this.maxHeatIntensity = 1.0;
                this.baseRadius = 15;
                this.maxRadius = 60;

                // Continuous drawing system
                this.lastMousePos = null;
                this.lastDrawTime = 0;
                this.drawingPath = [];
                this.isActivelyDrawing = false;

                // Performance and visual settings
                this.interpolationSteps = 5; // Points between mouse positions
                this.minDrawDistance = 2; // Minimum distance to trigger new drawing
                this.speedInfluenceRadius = 0.3; // How much speed affects radius
                this.speedInfluenceIntensity = 0.2; // How much speed affects intensity

                this.setupCanvas();
                this.setupEventListeners();
                this.updateStatus('Ready to draw - Press and hold for deeper heat traces');
            }

            setupCanvas() {
                // Set canvas size directly from HTML attributes
                const canvasWidth = this.canvas.width;
                const canvasHeight = this.canvas.height;

                // Ensure canvas has proper dimensions
                this.canvas.style.width = canvasWidth + 'px';
                this.canvas.style.height = canvasHeight + 'px';

                // Set initial canvas background
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, canvasWidth, canvasHeight);

                // Add debug info
                console.log('Canvas setup:', {
                    width: this.canvas.width,
                    height: this.canvas.height,
                    styleWidth: this.canvas.style.width,
                    styleHeight: this.canvas.style.height
                });
            }

            setupEventListeners() {
                // Mouse events
                this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
                this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
                this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
                this.canvas.addEventListener('mouseleave', (e) => this.handleMouseUp(e));

                // Touch events for mobile support
                this.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e));
                this.canvas.addEventListener('touchmove', (e) => this.handleTouchMove(e));
                this.canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e));

                // Clear button
                document.getElementById('clearCanvas').addEventListener('click', () => this.clearCanvas());

                // Prevent context menu on right click
                this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
            }

            getMousePos(e) {
                const rect = this.canvas.getBoundingClientRect();
                return {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };
            }

            getTouchPos(e) {
                const rect = this.canvas.getBoundingClientRect();
                return {
                    x: e.touches[0].clientX - rect.left,
                    y: e.touches[0].clientY - rect.top
                };
            }

            handleMouseDown(e) {
                this.isDrawing = true;
                this.isActivelyDrawing = true;
                const pos = this.getMousePos(e);
                this.lastMousePos = pos;
                this.lastDrawTime = Date.now();
                this.drawingPath = [pos];

                // Start with initial heat point
                this.drawContinuousHeat(pos.x, pos.y, 0, 0.5); // No speed, medium intensity
                this.updateStatus('Creating fluid heat trace...');
            }

            handleMouseMove(e) {
                if (!this.isDrawing) return;

                const currentPos = this.getMousePos(e);
                const currentTime = Date.now();

                if (this.lastMousePos) {
                    // Calculate movement metrics
                    const distance = this.getDistance(this.lastMousePos, currentPos);
                    const timeDelta = currentTime - this.lastDrawTime;
                    const speed = timeDelta > 0 ? distance / timeDelta : 0; // pixels per ms

                    // Only draw if moved enough to avoid redundant points
                    if (distance >= this.minDrawDistance) {
                        // Draw continuous path between last and current position
                        this.drawContinuousPath(this.lastMousePos, currentPos, speed);

                        // Update tracking variables
                        this.lastMousePos = currentPos;
                        this.lastDrawTime = currentTime;
                        this.drawingPath.push(currentPos);
                    }
                }
            }

            handleMouseUp(e) {
                if (this.isDrawing) {
                    this.isDrawing = false;
                    this.isActivelyDrawing = false;
                    this.drawingPath = [];
                    this.updateStatus('Ready to draw - Create fluid heat traces');
                }
            }

            handleTouchStart(e) {
                e.preventDefault();
                this.isDrawing = true;
                this.isActivelyDrawing = true;
                const pos = this.getTouchPos(e);
                this.lastMousePos = pos;
                this.lastDrawTime = Date.now();
                this.drawingPath = [pos];

                this.drawContinuousHeat(pos.x, pos.y, 0, 0.5);
                this.updateStatus('Creating fluid heat trace...');
            }

            handleTouchMove(e) {
                e.preventDefault();
                if (!this.isDrawing) return;

                const currentPos = this.getTouchPos(e);
                const currentTime = Date.now();

                if (this.lastMousePos) {
                    const distance = this.getDistance(this.lastMousePos, currentPos);
                    const timeDelta = currentTime - this.lastDrawTime;
                    const speed = timeDelta > 0 ? distance / timeDelta : 0;

                    if (distance >= this.minDrawDistance) {
                        this.drawContinuousPath(this.lastMousePos, currentPos, speed);
                        this.lastMousePos = currentPos;
                        this.lastDrawTime = currentTime;
                        this.drawingPath.push(currentPos);
                    }
                }
            }

            handleTouchEnd(e) {
                e.preventDefault();
                if (this.isDrawing) {
                    this.isDrawing = false;
                    this.isActivelyDrawing = false;
                    this.drawingPath = [];
                    this.updateStatus('Ready to draw - Create fluid heat traces');
                }
            }

            // Utility function to calculate distance between two points
            getDistance(pos1, pos2) {
                if (!pos1 || !pos2) return 0;
                const dx = pos1.x - pos2.x;
                const dy = pos1.y - pos2.y;
                return Math.sqrt(dx * dx + dy * dy);
            }

            // Draw continuous path between two points with interpolation
            drawContinuousPath(startPos, endPos, speed) {
                const distance = this.getDistance(startPos, endPos);
                if (distance < this.minDrawDistance) return;

                // Calculate number of interpolation steps based on distance
                const steps = Math.max(2, Math.ceil(distance / 3)); // Ensure smooth path

                for (let i = 0; i <= steps; i++) {
                    const t = i / steps; // Interpolation factor (0 to 1)

                    // Linear interpolation between start and end positions
                    const x = startPos.x + (endPos.x - startPos.x) * t;
                    const y = startPos.y + (endPos.y - startPos.y) * t;

                    // Calculate intensity based on position in path and speed
                    const baseIntensity = 0.3 + (0.4 * (1 - Math.min(speed * 100, 1))); // Slower = more intense
                    const pathIntensity = baseIntensity * (1 - t * 0.2); // Slightly fade along path

                    // Draw heat point at interpolated position
                    this.drawContinuousHeat(x, y, speed, pathIntensity);
                }
            }

            // Draw individual heat point optimized for continuous drawing
            drawContinuousHeat(x, y, speed, intensity) {
                // Calculate radius based on speed and base settings
                const speedFactor = 1 - Math.min(speed * this.speedInfluenceRadius, 0.7);
                const radius = this.baseRadius * speedFactor;

                // Adjust intensity based on speed
                const finalIntensity = intensity * (1 - Math.min(speed * this.speedInfluenceIntensity, 0.5));

                // Create optimized gradient for continuous drawing
                const gradient = this.ctx.createRadialGradient(x, y, 0, x, y, radius);

                // Simplified gradient for better performance
                gradient.addColorStop(0, `rgba(255, 0, 0, ${finalIntensity * 0.8})`);
                gradient.addColorStop(0.4, `rgba(255, 100, 0, ${finalIntensity * 0.5})`);
                gradient.addColorStop(0.7, `rgba(100, 150, 255, ${finalIntensity * 0.3})`);
                gradient.addColorStop(1, `rgba(150, 150, 150, ${finalIntensity * 0.1})`);

                // Use lighter blend mode for fluid accumulation effect
                this.ctx.globalCompositeOperation = 'lighter';
                this.ctx.fillStyle = gradient;
                this.ctx.beginPath();
                this.ctx.arc(x, y, radius, 0, Math.PI * 2);
                this.ctx.fill();

                // Reset blend mode
                this.ctx.globalCompositeOperation = 'source-over';
            }



            clearCanvas() {
                // Clear the canvas
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                // Reset drawing state
                this.isDrawing = false;
                this.isActivelyDrawing = false;
                this.lastMousePos = null;
                this.drawingPath = [];

                this.updateStatus('Canvas cleared - Ready to create fluid heat traces');
            }

            updateStatus(message) {
                this.statusText.textContent = message;
            }
        }

        // Initialize the application when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            const heatmapDrawer = new HeatmapDrawer('heatmapCanvas');
        });
    </script>
</body>
</html>