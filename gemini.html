<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互式热力图画布</title>
    <!-- 引入 Tailwind CSS 用于快速布局和样式设计 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入 heatmap.js 库 -->
    <script src="https://cdn.jsdelivr.net/npm/heatmap.js@2.0.2/heatmap.min.js"></script>
    <style>
        /* 自定义滑块样式，使其在不同浏览器中外观更一致 */
        input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
            width: 100%;
            height: 8px;
            background: #d3d3d3;
            border-radius: 5px;
            outline: none;
            opacity: 0.7;
            transition: opacity .2s;
        }

        input[type="range"]:hover {
            opacity: 1;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: #4A90E2;
            cursor: pointer;
            border-radius: 50%;
        }

        input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: #4A90E2;
            cursor: pointer;
            border-radius: 50%;
        }
        /* 主容器样式，确保它占据整个视口 */
        body, html {
            height: 100%;
            margin: 0;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f9fa;
        }
        /* Gooey/Metaball effect using SVG filter */
        #filter-container {
            filter: url(#goo);
        }
    </style>
</head>
<body class="flex items-center justify-center p-4">

    <!-- SVG滤镜定义区 -->
    <svg style="position: absolute; width: 0; height: 0;">
      <defs>
        <filter id="goo">
          <feGaussianBlur in="SourceGraphic" stdDeviation="8" result="blur" />
          <feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 20 -9" result="goo" />
          <feComposite in="SourceGraphic" in2="goo" operator="atop"/>
        </filter>
      </defs>
    </svg>

    <div class="w-full max-w-7xl h-[80vh] flex bg-white shadow-2xl rounded-2xl overflow-hidden">
        
        <!-- Canvas 容器 -->
        <div id="canvas-wrapper" class="flex-grow h-full bg-white relative">
            <!-- 顶层可见画布 -->
            <canvas id="visibleCanvas" class="absolute inset-0"></canvas>
            <!-- 底层heatmap.js画布的容器（将被隐藏） -->
            <div id="heatmapContainer" class="absolute inset-0"></div>
        </div>

        <!-- 控制面板 -->
        <div class="w-72 bg-gray-50 p-6 border-l border-gray-200 flex flex-col space-y-6">
            <h2 class="text-xl font-bold text-gray-800">控制面板</h2>
            
            <!-- 半径 (Radius) 控制 -->
            <div>
                <label for="radiusSlider" class="block text-sm font-medium text-gray-700">
                    半径 (Radius): <span id="radiusValue" class="font-bold">20</span>
                </label>
                <input id="radiusSlider" type="range" min="10" max="100" value="20" class="mt-2">
            </div>

            <!-- 模糊 (Blur) 控制 -->
            <div>
                <label for="blurSlider" class="block text-sm font-medium text-gray-700">
                    模糊 (Blur): <span id="blurValue" class="font-bold">0.75</span>
                </label>
                <input id="blurSlider" type="range" min="0" max="1" step="0.05" value="0.75" class="mt-2">
            </div>

            <!-- 最大不透明度 (Max Opacity) 控制 -->
            <div>
                <label for="opacitySlider" class="block text-sm font-medium text-gray-700">
                    最大不透明度: <span id="opacityValue" class="font-bold">0.6</span>
                </label>
                <input id="opacitySlider" type="range" min="0" max="1" step="0.05" value="0.6" class="mt-2">
            </div>
            
            <!-- 调色板预览 -->
            <div>
                 <label class="block text-sm font-medium text-gray-700 mb-2">调色板 (Palette)</label>
                 <div class="w-full h-4 rounded-md" style="background: linear-gradient(to right, #BEBEBE, #00BFFF, #FF0000);"></div>
            </div>
            
            <div class="flex-grow"></div>

            <!-- 操作按钮 -->
            <div class="flex flex-col space-y-3 pt-4 border-t border-gray-200">
                <button id="clearBtn" class="w-full bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                    清空画布
                </button>
            </div>
        </div>
    </div>

    <script>
        window.onload = function() {
            // --- 1. 初始化 ---
            const wrapper = document.getElementById('canvas-wrapper');
            const visibleCanvas = document.getElementById('visibleCanvas');
            const visibleCtx = visibleCanvas.getContext('2d');
            const heatmapContainer = document.getElementById('heatmapContainer');
            
            let points = []; // 用于存储所有热力点
            const max = 100; // 最大热力值
            const decayRate = 1; // 每次动画帧的衰减率
            const minValue = 30; // 慢速滑动时，热力衰减到的最小值

            let lastX = 0, lastY = 0, lastTime = Date.now();
            const speedThreshold = 600;

            // 设置画布尺寸
            function resizeCanvas() {
                visibleCanvas.width = wrapper.offsetWidth;
                visibleCanvas.height = wrapper.offsetHeight;
                heatmapContainer.style.width = wrapper.offsetWidth + 'px';
                heatmapContainer.style.height = wrapper.offsetHeight + 'px';
            }
            resizeCanvas();

            const heatmapInstance = h337.create({
                container: heatmapContainer,
                radius: 20,
                maxOpacity: 1, // Set to 1 for better pixel manipulation
                minOpacity: 0,
                blur: 0.75,
                gradient: { '.3': '#BEBEBE', '.65': '#00BFFF', '.98': '#FF0000' }
            });
            // Apply gooey filter to the heatmap container
            heatmapContainer.style.filter = 'url(#goo)';


            // --- 2. 动画循环 ---
            function animate() {
                let needsUpdate = false; 

                if (points.length > 0) {
                    for (let i = 0; i < points.length; i++) {
                        const targetValue = points[i].isTemporary ? 0 : minValue;
                        if (points[i].value > targetValue) {
                            points[i].value = Math.max(targetValue, points[i].value - decayRate);
                            needsUpdate = true;
                        }
                    }

                    const oldPointCount = points.length;
                    points = points.filter(p => !p.isTemporary || p.value > 0);
                    if (oldPointCount !== points.length) {
                        needsUpdate = true;
                    }
                    
                    if(needsUpdate) {
                        heatmapInstance.setData({ max: max, data: points });
                    }
                }
                
                // Pixel manipulation for grain effect
                applyGrain();
                
                requestAnimationFrame(animate);
            }
            
            // --- 3. 颗粒效果函数 ---
            function applyGrain() {
                const heatmapCanvas = heatmapContainer.querySelector('canvas');
                if (!heatmapCanvas) return;

                visibleCtx.clearRect(0, 0, visibleCanvas.width, visibleCanvas.height);
                // Draw the gooey heatmap first
                visibleCtx.drawImage(heatmapCanvas, 0, 0);

                const imageData = visibleCtx.getImageData(0, 0, visibleCanvas.width, visibleCanvas.height);
                const pixels = imageData.data;
                const grainAmount = 25; // 控制颗粒强度

                for (let i = 0; i < pixels.length; i += 4) {
                    // 只为非透明像素添加颗粒
                    if (pixels[i + 3] > 0) {
                        const grain = (Math.random() - 0.5) * grainAmount;
                        pixels[i] += grain;
                        pixels[i + 1] += grain;
                        pixels[i + 2] += grain;
                    }
                }
                visibleCtx.putImageData(imageData, 0, 0);
            }

            animate(); // 启动动画

            // --- 4. 交互式绘制 ---
            wrapper.addEventListener('mousemove', function(event) {
                const rect = wrapper.getBoundingClientRect();
                const x = Math.round(event.clientX - rect.left);
                const y = Math.round(event.clientY - rect.top);
                const currentTime = Date.now();

                const timeDiff = currentTime - lastTime;
                if (timeDiff > 16) { 
                    const distance = Math.sqrt(Math.pow(x - lastX, 2) + Math.pow(y - lastY, 2));
                    const speed = distance / (timeDiff / 1000); 
                    const isTemporary = speed > speedThreshold;
                    points.push({ x: x, y: y, value: max, isTemporary: isTemporary });
                    lastX = x;
                    lastY = y;
                    lastTime = currentTime;
                }
            });

            // --- 5. 控制面板逻辑 ---
            const radiusSlider = document.getElementById('radiusSlider');
            const blurSlider = document.getElementById('blurSlider');
            const opacitySlider = document.getElementById('opacitySlider');
            const radiusValue = document.getElementById('radiusValue');
            const blurValue = document.getElementById('blurValue');
            const opacityValue = document.getElementById('opacityValue');
            const clearBtn = document.getElementById('clearBtn');
            
            function updateConfig() {
                heatmapInstance.configure({
                    radius: parseInt(radiusSlider.value, 10),
                    blur: parseFloat(blurSlider.value),
                    maxOpacity: 1 // Keep at 1 for pixel manipulation
                });
                radiusValue.textContent = radiusSlider.value;
                blurValue.textContent = parseFloat(blurSlider.value).toFixed(2);
                // Opacity is now controlled by grain, so this slider is visual only
                // opacityValue.textContent = parseFloat(opacitySlider.value).toFixed(2);
            }

            radiusSlider.addEventListener('input', updateConfig);
            blurSlider.addEventListener('input', updateConfig);
            // Opacity slider doesn't have a direct effect now, can be removed or repurposed
            
            clearBtn.addEventListener('click', function() {
                points = []; 
                heatmapInstance.setData({ max: max, data: [] });
                // Also clear the visible canvas immediately
                visibleCtx.clearRect(0, 0, visibleCanvas.width, visibleCanvas.height);
            });
            
             window.addEventListener('resize', function() {
                resizeCanvas();
                // No need to recreate heatmap instance, it resizes automatically
            });
            
            updateConfig();
        };
    </script>
</body>
</html>
